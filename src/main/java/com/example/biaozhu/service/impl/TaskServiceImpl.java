package com.example.biaozhu.service.impl;

import com.example.biaozhu.entity.Dataset;
import com.example.biaozhu.entity.Project;
import com.example.biaozhu.entity.Task;
import com.example.biaozhu.entity.User;
import com.example.biaozhu.exception.ResourceNotFoundException;
import com.example.biaozhu.payload.request.TaskAssignmentRequest;
import com.example.biaozhu.payload.request.TaskRequest;
import com.example.biaozhu.repository.AnnotationRepository;
import com.example.biaozhu.repository.DatasetRepository;
import com.example.biaozhu.repository.ProjectRepository;
import com.example.biaozhu.repository.TaskRepository;
import com.example.biaozhu.repository.UserRepository;
import com.example.biaozhu.service.TaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务服务实现类
 */
@Service
public class TaskServiceImpl implements TaskService {

    private final TaskRepository taskRepository;
    private final ProjectRepository projectRepository;
    private final DatasetRepository datasetRepository;
    private final UserRepository userRepository;
    private final AnnotationRepository annotationRepository;

    @Autowired
    public TaskServiceImpl(TaskRepository taskRepository,
                          ProjectRepository projectRepository,
                          DatasetRepository datasetRepository,
                          UserRepository userRepository,
                          AnnotationRepository annotationRepository) {
        this.taskRepository = taskRepository;
        this.projectRepository = projectRepository;
        this.datasetRepository = datasetRepository;
        this.userRepository = userRepository;
        this.annotationRepository = annotationRepository;
    }

    @Override
    public List<Task> getAllTasks() {
        return taskRepository.findAll();
    }

    @Override
    public Page<Task> getTasks(Pageable pageable) {
        return taskRepository.findAll(pageable);
    }

    @Override
    public List<Task> getTasksByProject(Long projectId) {
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + projectId));
        return taskRepository.findByProject(project);
    }

    @Override
    public List<Task> getTasksByDataset(Long datasetId) {
        Dataset dataset = datasetRepository.findById(datasetId)
                .orElseThrow(() -> new ResourceNotFoundException("Dataset not found with id: " + datasetId));
        return taskRepository.findByDataset(dataset);
    }

    @Override
    public Task getTaskById(Long id) {
        return taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + id));
    }

    @Override
    @Transactional
    public Task createTask(Task task) {
        String username = getCurrentUsername();
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));
        
        if (task.getProject() != null && task.getProject().getId() != null) {
            Project project = projectRepository.findById(task.getProject().getId())
                    .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + task.getProject().getId()));
            task.setProject(project);
        }
        
        if (task.getDataset() != null && task.getDataset().getId() != null) {
            Dataset dataset = datasetRepository.findById(task.getDataset().getId())
                    .orElseThrow(() -> new ResourceNotFoundException("Dataset not found with id: " + task.getDataset().getId()));
            task.setDataset(dataset);
        }
        
        task.setCreatedBy(user);
        task.setCreatedAt(LocalDateTime.now());
        task.setUpdatedAt(LocalDateTime.now());
        task.setStatus("PENDING");
        task.setProgress(0.0);
        
        return taskRepository.save(task);
    }

    @Override
    @Transactional
    public Task updateTask(Long id, Task taskDetails) {
        Task task = taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + id));
        
        // 检查权限
        checkTaskAccess(task);
        
        task.setName(taskDetails.getName());
        task.setDescription(taskDetails.getDescription());
        task.setTaskType(taskDetails.getTaskType());
        task.setDeadline(taskDetails.getDeadline());
        task.setUpdatedAt(LocalDateTime.now());
        
        if (taskDetails.getProject() != null && taskDetails.getProject().getId() != null) {
            Project project = projectRepository.findById(taskDetails.getProject().getId())
                    .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + taskDetails.getProject().getId()));
            task.setProject(project);
        }
        
        if (taskDetails.getDataset() != null && taskDetails.getDataset().getId() != null) {
            Dataset dataset = datasetRepository.findById(taskDetails.getDataset().getId())
                    .orElseThrow(() -> new ResourceNotFoundException("Dataset not found with id: " + taskDetails.getDataset().getId()));
            task.setDataset(dataset);
        }
        
        return taskRepository.save(task);
    }

    @Override
    @Transactional
    public void deleteTask(Long id) {
        Task task = taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + id));
        
        // 检查权限
        checkTaskAccess(task);
        
        // 首先删除相关的标注
        annotationRepository.deleteByTask(task);
        
        // 然后删除任务
        taskRepository.delete(task);
    }

    @Override
    @Transactional
    public Task assignTask(Long taskId, Long userId) {
        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + taskId));
        
        // 检查权限
        checkTaskAccess(task);
        
        User assignee = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));
        
        task.setAssignedTo(assignee);
        task.setAssignedAt(java.util.Date.from(LocalDateTime.now().atZone(java.time.ZoneId.systemDefault()).toInstant()));
        task.setStatus("IN_PROGRESS");
        task.setUpdatedAt(LocalDateTime.now());
        
        return taskRepository.save(task);
    }

    @Override
    @Transactional
    public Task submitTask(Long taskId) {
        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + taskId));
        
        // 检查是否是任务执行者
        String username = getCurrentUsername();
        User currentUser = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));
        
        if (task.getAssignedTo() == null || !task.getAssignedTo().getId().equals(currentUser.getId())) {
            throw new RuntimeException("You are not assigned to this task");
        }
        
        task.setStatus("UNDER_REVIEW");
        task.setSubmittedAt(java.util.Date.from(LocalDateTime.now().atZone(java.time.ZoneId.systemDefault()).toInstant()));
        task.setUpdatedAt(LocalDateTime.now());
        task.setProgress(100.0);
        
        return taskRepository.save(task);
    }

    @Override
    @Transactional
    public Task approveTask(Long taskId, String reviewComments) {
        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + taskId));
        
        // 检查是否是审核者或管理员
        String username = getCurrentUsername();
        User currentUser = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));
        
        if (task.getReviewer() != null && !task.getReviewer().getId().equals(currentUser.getId()) &&
                !currentUser.getRoles().stream().anyMatch(role -> role.getName().equals("ADMIN"))) {
            throw new RuntimeException("You are not authorized to review this task");
        }
        
        task.setStatus("COMPLETED");
        task.setReviewedAt(java.util.Date.from(LocalDateTime.now().atZone(java.time.ZoneId.systemDefault()).toInstant()));
        task.setReviewComments(reviewComments);
        task.setUpdatedAt(LocalDateTime.now());
        
        if (task.getReviewer() == null) {
            task.setReviewer(currentUser);
        }
        
        return taskRepository.save(task);
    }

    @Override
    @Transactional
    public Task rejectTask(Long taskId, String reviewComments) {
        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + taskId));
        
        // 检查是否是审核者或管理员
        String username = getCurrentUsername();
        User currentUser = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));
        
        if (task.getReviewer() != null && !task.getReviewer().getId().equals(currentUser.getId()) &&
                !currentUser.getRoles().stream().anyMatch(role -> role.getName().equals("ADMIN"))) {
            throw new RuntimeException("You are not authorized to review this task");
        }
        
        task.setStatus("REJECTED");
        task.setReviewedAt(java.util.Date.from(LocalDateTime.now().atZone(java.time.ZoneId.systemDefault()).toInstant()));
        task.setReviewComments(reviewComments);
        task.setUpdatedAt(LocalDateTime.now());
        
        if (task.getReviewer() == null) {
            task.setReviewer(currentUser);
        }
        
        return taskRepository.save(task);
    }

    @Override
    public List<Task> getTasksAssignedToUser(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));
        return taskRepository.findByAssignedTo(user);
    }

    @Override
    public List<Task> getTasksToReviewByUser(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));
        return taskRepository.findByReviewer(user);
    }

    @Override
    public Map<String, Object> getTaskStatistics(Long taskId) {
        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + taskId));

        // 计算已完成的标注数量（状态为SUBMITTED、REVIEWED或APPROVED的标注）
        long completedAnnotations = annotationRepository.countByTaskIdAndStatus(taskId, "SUBMITTED") +
                                   annotationRepository.countByTaskIdAndStatus(taskId, "REVIEWED") +
                                   annotationRepository.countByTaskIdAndStatus(taskId, "APPROVED");

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("id", task.getId());
        statistics.put("name", task.getName());
        statistics.put("status", task.getStatus());
        statistics.put("assignedTo", task.getAssignedTo() != null ? task.getAssignedTo().getUsername() : null);
        statistics.put("progress", task.getProgress());
        statistics.put("annotationCount", task.getAnnotationCount());
        statistics.put("completedAnnotations", (int) completedAnnotations);

        return statistics;
    }

    @Override
    public Map<String, Object> getProjectTasksStatistics(Long projectId) {
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + projectId));
        
        List<Task> tasks = taskRepository.findByProject(project);
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalTasks", tasks.size());
        
        long pendingTasks = tasks.stream().filter(t -> "PENDING".equals(t.getStatus())).count();
        long inProgressTasks = tasks.stream().filter(t -> "IN_PROGRESS".equals(t.getStatus())).count();
        long completedTasks = tasks.stream().filter(t -> "COMPLETED".equals(t.getStatus())).count();
        long reviewedTasks = tasks.stream().filter(t -> "REVIEWED".equals(t.getStatus())).count();
        
        statistics.put("pendingTasks", pendingTasks);
        statistics.put("inProgressTasks", inProgressTasks);
        statistics.put("completedTasks", completedTasks);
        statistics.put("reviewedTasks", reviewedTasks);
        
        if (!tasks.isEmpty()) {
            double overallProgress = tasks.stream().mapToDouble(Task::getProgress).sum() / tasks.size();
            statistics.put("overallProgress", overallProgress);
        } else {
            statistics.put("overallProgress", 0.0);
        }
        
        return statistics;
    }
    
    private String getCurrentUsername() {
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal instanceof UserDetails) {
            return ((UserDetails) principal).getUsername();
        }
        return principal.toString();
    }
    
    // 检查用户是否有权限操作任务
    private void checkTaskAccess(Task task) {
        String username = getCurrentUsername();
        User currentUser = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));
        
        // 如果是管理员、项目创建者或任务创建者，允许操作
        boolean isAdmin = currentUser.getRoles().stream().anyMatch(role -> "ADMIN".equals(role.getName().toString()));
        boolean isProjectCreator = task.getProject() != null && 
                task.getProject().getCreatedBy() != null && 
                task.getProject().getCreatedBy().getUsername().equals(currentUser.getUsername());
        
        boolean isTaskCreator = false;
        if (task.getCreator() != null && task.getCreator().getUsername().equals(currentUser.getUsername())) {
            isTaskCreator = true;
        } else if (task.getCreatedBy() != null && task.getCreatedBy().getUsername().equals(currentUser.getUsername())) {
            isTaskCreator = true;
        }
        
        if (!isAdmin && !isProjectCreator && !isTaskCreator) {
            throw new RuntimeException("You don't have permission to modify this task");
        }
    }

    /**
     * 分页获取所有任务
     * 
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 分页任务结果
     */
    public Page<Task> getAllTasks(int page, int size, String sort) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, sort));
        return taskRepository.findAll(pageable);
    }
    
    /**
     * 根据请求对象创建任务
     * 
     * @param taskRequest 任务请求对象
     * @param creator 创建者
     * @return 创建的任务
     */
    @Transactional
    public Task createTask(TaskRequest taskRequest, User creator) {
        // 获取数据集
        Dataset dataset = datasetRepository.findById(taskRequest.getDatasetId())
                .orElseThrow(() -> new ResourceNotFoundException("Dataset not found with id: " + taskRequest.getDatasetId()));
        
        // 创建任务实体
        Task task = new Task();
        task.setName(taskRequest.getName());
        task.setDescription(taskRequest.getDescription());
        task.setTaskType(taskRequest.getTaskType());
        task.setStatus("CREATED");
        task.setCreator(creator);
        task.setDataset(dataset);
        task.setPriority(taskRequest.getPriority() != null ? taskRequest.getPriority() : 0);
        task.setStartDate(taskRequest.getStartDate());
        task.setDueDate(taskRequest.getDueDate());
        task.setCreatedAt(LocalDateTime.now());
        task.setUpdatedAt(LocalDateTime.now());
        
        // 如果指定了项目
        if (taskRequest.getProjectId() != null) {
            Project project = projectRepository.findById(taskRequest.getProjectId())
                    .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + taskRequest.getProjectId()));
            task.setProject(project);
        }
        
        // 如果指定了分配给的用户
        if (taskRequest.getAssignedToId() != null) {
            User assignedTo = userRepository.findById(taskRequest.getAssignedToId())
                    .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + taskRequest.getAssignedToId()));
            task.setAssignedTo(assignedTo);
            task.setStatus("ASSIGNED");
        }
        
        // 如果指定了审核人
        if (taskRequest.getReviewerId() != null) {
            User reviewer = userRepository.findById(taskRequest.getReviewerId())
                    .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + taskRequest.getReviewerId()));
            task.setReviewer(reviewer);
        }
        
        return taskRepository.save(task);
    }
    
    /**
     * 更新任务
     * 
     * @param id 任务ID
     * @param taskRequest 任务请求对象
     * @return 更新后的任务
     */
    @Transactional
    public Task updateTask(Long id, TaskRequest taskRequest) {
        Task task = taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + id));
        
        task.setName(taskRequest.getName());
        task.setDescription(taskRequest.getDescription());
        task.setTaskType(taskRequest.getTaskType());
        
        if (taskRequest.getPriority() != null) {
            task.setPriority(taskRequest.getPriority());
        }
        
        if (taskRequest.getStartDate() != null) {
            task.setStartDate(taskRequest.getStartDate());
        }
        
        if (taskRequest.getDueDate() != null) {
            task.setDueDate(taskRequest.getDueDate());
        }
        
        // 更新数据集
        if (taskRequest.getDatasetId() != null && 
            (task.getDataset() == null || !task.getDataset().getId().equals(taskRequest.getDatasetId()))) {
            Dataset dataset = datasetRepository.findById(taskRequest.getDatasetId())
                    .orElseThrow(() -> new ResourceNotFoundException("Dataset not found with id: " + taskRequest.getDatasetId()));
            task.setDataset(dataset);
        }
        
        // 更新项目
        if (taskRequest.getProjectId() != null) {
            Project project = projectRepository.findById(taskRequest.getProjectId())
                    .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + taskRequest.getProjectId()));
            task.setProject(project);
        }
        
        // 更新分配给的用户
        if (taskRequest.getAssignedToId() != null) {
            User assignedTo = userRepository.findById(taskRequest.getAssignedToId())
                    .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + taskRequest.getAssignedToId()));
            task.setAssignedTo(assignedTo);
            
            // 如果任务状态是"CREATED"，则更新为"ASSIGNED"
            if ("CREATED".equals(task.getStatus())) {
                task.setStatus("ASSIGNED");
            }
        }
        
        // 更新审核人
        if (taskRequest.getReviewerId() != null) {
            User reviewer = userRepository.findById(taskRequest.getReviewerId())
                    .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + taskRequest.getReviewerId()));
            task.setReviewer(reviewer);
        }
        
        task.setUpdatedAt(LocalDateTime.now());
        
        return taskRepository.save(task);
    }
    
    /**
     * 根据项目ID和状态获取任务
     * 
     * @param projectId 项目ID
     * @param status 任务状态
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 分页任务结果
     */
    public Page<Task> getTasksByProject(Long projectId, String status, int page, int size, String sort) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, sort));
        
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + projectId));
        
        if (status != null && !status.isEmpty()) {
            return taskRepository.findByProjectAndStatus(project, status, pageable);
        } else {
            return taskRepository.findByProject(project, pageable);
        }
    }
    
    /**
     * 根据用户和状态获取任务
     * 
     * @param user 用户
     * @param status 任务状态
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 分页任务结果
     */
    public Page<Task> getTasksByUser(User user, String status, int page, int size, String sort) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, sort));
        
        if (status != null && !status.isEmpty()) {
            return taskRepository.findByAssignedToAndStatus(user, status, pageable);
        } else {
            return taskRepository.findByAssignedTo(user, pageable);
        }
    }
    
    /**
     * 任务分配
     * 
     * @param taskId 任务ID
     * @param request 任务分配请求
     * @return 更新后的任务
     */
    @Transactional
    public Task assignTask(Long taskId, TaskAssignmentRequest request) {
        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + taskId));
        
        User assignee = userRepository.findById(request.getAssigneeId())
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + request.getAssigneeId()));
        
        task.setAssignedTo(assignee);
        task.setStatus("ASSIGNED");
        task.setUpdatedAt(LocalDateTime.now());
        
        return taskRepository.save(task);
    }
    
    /**
     * 开始任务
     * 
     * @param taskId 任务ID
     * @return 更新后的任务
     */
    @Transactional
    public Task startTask(Long taskId) {
        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + taskId));
        
        task.setStatus("IN_PROGRESS");
        task.setStartDate(LocalDateTime.now());
        task.setUpdatedAt(LocalDateTime.now());
        
        return taskRepository.save(task);
    }
    
    /**
     * 完成任务
     * 
     * @param taskId 任务ID
     * @return 更新后的任务
     */
    @Transactional
    public Task completeTask(Long taskId) {
        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + taskId));
        
        task.setStatus("COMPLETED");
        task.setCompletionDate(LocalDateTime.now());
        task.setUpdatedAt(LocalDateTime.now());
        
        return taskRepository.save(task);
    }
    
    /**
     * 审核任务
     * 
     * @param taskId 任务ID
     * @param reviewer 审核人
     * @param approved 是否批准
     * @param comments 审核意见
     * @return 更新后的任务
     */
    @Transactional
    public Task reviewTask(Long taskId, User reviewer, boolean approved, String comments) {
        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + taskId));
        
        task.setReviewer(reviewer);
        task.setStatus(approved ? "REVIEWED" : "REJECTED");
        task.setReviewComments(comments);
        task.setUpdatedAt(LocalDateTime.now());
        
        return taskRepository.save(task);
    }
    
    /**
     * 获取任务状态统计
     * 
     * @return 状态统计信息
     */
    public Map<String, Long> getTaskStatusStatistics() {
        List<Object[]> results = taskRepository.countByStatus();
        Map<String, Long> statistics = new HashMap<>();
        
        for (Object[] result : results) {
            String status = (String) result[0];
            Long count = ((Number) result[1]).longValue();
            statistics.put(status, count);
        }
        
        return statistics;
    }
    
    /**
     * 获取标注员绩效
     * 
     * @param days 天数
     * @return 绩效统计信息
     */
    public List<Map<String, Object>> getAnnotatorPerformance(int days) {
        // 此处实现标注员绩效统计逻辑
        // 示例实现，实际应根据业务需求查询数据库
        return Collections.emptyList();
    }
    
    /**
     * 导出任务数据
     * 
     * @param taskId 任务ID
     * @param format 导出格式
     * @return 导出的数据
     */
    public byte[] exportTaskData(Long taskId, String format) {
        // 此处实现导出任务数据逻辑
        // 示例实现，实际应根据业务需求查询数据库并格式化数据
        return new byte[0];
    }
} 